// Minimal functions index - only essential webhook functionality
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
admin.initializeApp();

// Import simple admin notification functions
export * from './simple-admin-notifications';

// Import notification functions
export * from './notifications';

// Helper function to generate 6-digit secret code
function generateSecretCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Essential Stripe webhook - only handles payment completion
export const essentialWebhook = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onRequest(async (req, res) => {
    try {
      console.log('🔗 Essential webhook received');

      if (req.method !== 'POST') {
        res.status(405).send('Method not allowed');
        return;
      }

      const event = req.body;
      console.log(`📨 Event type: ${event.type}`);

      // Handle checkout session completed
      if (event.type === 'checkout.session.completed') {
        const session = event.data.object;
        const metadata = session.metadata;

        if (metadata?.orderId) {
          const orderId = metadata.orderId;
          console.log(`📦 Processing order: ${orderId}`);

          // Get order
          const orderRef = admin.firestore().collection('orders').doc(orderId);
          const orderDoc = await orderRef.get();

          if (orderDoc.exists) {
            const orderData = orderDoc.data();
            
            // Generate secret code
            const secretCode = generateSecretCode();
            console.log(`🔐 Generated code: ${secretCode}`);

            // Update order
            await orderRef.update({
              status: 'payment_succeeded',
              secretCode: secretCode,
              paymentCompletedAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });

            // Update listing to sold
            if (orderData?.listingId) {
              await admin.firestore().collection('listings').doc(orderData.listingId).update({
                status: 'sold',
                soldAt: admin.firestore.Timestamp.now(),
                updatedAt: admin.firestore.Timestamp.now()
              });
              console.log(`✅ Listing ${orderData.listingId} marked as sold`);
            }

            // Send buyer notification using new notification system
            if (orderData?.buyerId) {
              try {
                // Create in-app notification directly (since we're in the backend)
                await admin.firestore().collection(`users/${orderData.buyerId}/notifications`).add({
                  type: 'payment_success',
                  title: 'Payment Successful!',
                  message: `Your payment has been processed. Secret code: ${secretCode}`,
                  icon: '/icons/icon-192.png',
                  createdAt: admin.firestore.Timestamp.now(),
                  read: false,
                  link: `/orders/${orderId}`,
                  priority: 'high',
                  actionRequired: false,
                  metadata: {},
                  orderId: orderId,
                  secretCode: secretCode,
                  amount: orderData.totalAmount
                });

                // Also send push notification if user has FCM token
                const tokenDoc = await admin.firestore()
                  .doc(`users/${orderData.buyerId}/fcmTokens/web`)
                  .get();

                if (tokenDoc.exists && tokenDoc.data()?.active && tokenDoc.data()?.token) {
                  const payload: admin.messaging.Message = {
                    token: tokenDoc.data()!.token,
                    notification: {
                      title: 'Payment Successful!',
                      body: `Your payment has been processed. Secret code: ${secretCode}`,
                      imageUrl: '/icons/icon-192.png'
                    },
                    data: {
                      type: 'payment_success',
                      link: `/orders/${orderId}`,
                      orderId: orderId,
                      requireInteraction: 'true'
                    },
                    webpush: {
                      headers: {
                        TTL: '86400',
                        Urgency: 'high'
                      },
                      notification: {
                        icon: '/icons/icon-192.png',
                        badge: '/icons/icon-96.png',
                        tag: 'payment_success',
                        requireInteraction: true,
                        actions: [
                          { action: 'view', title: 'View Order' },
                          { action: 'dismiss', title: 'Dismiss' }
                        ],
                        vibrate: [200, 100, 200]
                      }
                    }
                  };

                  try {
                    await admin.messaging().send(payload);
                    console.log(`Push notification sent for payment success to user ${orderData.buyerId}`);
                  } catch (pushError) {
                    console.error('Error sending push notification:', pushError);
                    // Mark token as inactive if it's invalid
                    if ((pushError as any).code === 'messaging/registration-token-not-registered') {
                      await admin.firestore()
                        .doc(`users/${orderData.buyerId}/fcmTokens/web`)
                        .update({ active: false });
                    }
                  }
                }
              } catch (notificationError) {
                console.error('Error sending payment success notification:', notificationError);
                // Don't fail the webhook if notification fails
              }
            }

            // Create admin notification for payment completion - temporarily disabled
            // try {
            //   if (orderData) {
            //     // Get user data for better notification
            //     const buyerDoc = await admin.firestore().collection('users').doc(orderData.buyerId).get();
            //     const buyerData = buyerDoc.exists ? buyerDoc.data() : null;
            //     const buyerName = buyerData?.name || buyerData?.displayName || buyerData?.email?.split('@')[0] || 'User';

            //     await createAdminNotification(
            //       'payment_completed',
            //       'Payment Completed',
            //       `${buyerName} completed payment of $${orderData.totalAmount}`,
            //       {
            //         userId: orderData.buyerId,
            //         username: buyerName,
            //         orderId: orderId,
            //         amount: orderData.totalAmount,
            //         metadata: {
            //           secretCode: secretCode,
            //           listingId: orderData.listingId,
            //           sellerId: orderData.sellerId
            //         },
            //         actionUrl: `/admin/transactions?search=${orderId}`
            //       }
            //     );
            //   }
            // } catch (notificationError) {
            //   console.error('Error creating admin notification:', notificationError);
            //   // Don't fail the webhook if notification fails
            // }

            console.log(`✅ Order ${orderId} processed successfully`);
          }
        }
      }

      // Handle payment failures
      if (event.type === 'payment_intent.payment_failed') {
        const paymentIntent = event.data.object;
        const metadata = paymentIntent.metadata;

        if (metadata?.orderId) {
          const orderId = metadata.orderId;
          console.log(`❌ Payment failed for order: ${orderId}`);

          try {
            // Get order
            const orderRef = admin.firestore().collection('orders').doc(orderId);
            const orderDoc = await orderRef.get();

            if (orderDoc.exists) {
              const orderData = orderDoc.data();

              if (orderData) {
                // Update order status
                await orderRef.update({
                  status: 'payment_failed',
                  paymentFailedAt: admin.firestore.Timestamp.now(),
                  updatedAt: admin.firestore.Timestamp.now()
                });

                // Get user data for notification
                const buyerDoc = await admin.firestore().collection('users').doc(orderData.buyerId).get();
                const buyerData = buyerDoc.exists ? buyerDoc.data() : null;
                const buyerName = buyerData?.name || buyerData?.displayName || buyerData?.email?.split('@')[0] || 'User';

                // Create admin notification for payment failure - temporarily disabled
                // await createAdminNotification(
                //   'payment_failed',
                //   'Payment Failed',
                //   `Payment failed for ${buyerName}'s order of $${orderData.totalAmount}`,
                //   {
                //     userId: orderData.buyerId,
                //     username: buyerName,
                //     orderId: orderId,
                //     amount: orderData.totalAmount,
                //     metadata: {
                //       failureReason: paymentIntent.last_payment_error?.message || 'Unknown error',
                //       listingId: orderData.listingId,
                //       sellerId: orderData.sellerId
                //     },
                //     actionUrl: `/admin/transactions?search=${orderId}`
                //   }
                // );

                console.log(`❌ Payment failure processed for order: ${orderId}`);
              }
            }
          } catch (error) {
            console.error('Error processing payment failure:', error);
          }
        }
      }

      res.status(200).json({ received: true });

    } catch (error) {
      console.error('❌ Webhook error:', error);
      res.status(500).send('Webhook failed');
    }
  });

// Test function
export const testEssential = functions
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Essential webhook working',
      testCode: generateSecretCode(),
      timestamp: new Date().toISOString()
    });
  });

// Release funds with secret code (alias for compatibility)
export const releaseEscrowWithCode = functions
  .https.onCall(async (data, context) => {
    try {
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
      }

      const { orderId, secretCode } = data;
      
      if (!orderId || !secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code required');
      }

      // Get order
      const orderRef = admin.firestore().collection('orders').doc(orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      // Verify buyer
      if (orderData?.buyerId !== context.auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'Not authorized');
      }

      // Verify secret code
      if (orderData?.secretCode !== secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
      }

      // Update order status
      await orderRef.update({
        status: 'completed',
        fundsReleased: true,
        fundsReleasedAt: admin.firestore.Timestamp.now(),
        completedAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });

      console.log(`✅ Funds released for order: ${orderId}`);

      return {
        success: true,
        message: 'Funds released successfully'
      };

    } catch (error) {
      console.error('Error releasing funds:', error);
      throw error;
    }
  });

// Release funds with secret code (new name)
export const releaseFundsWithCode = functions
  .https.onCall(async (data, context) => {
    try {
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
      }

      const { orderId, secretCode } = data;
      
      if (!orderId || !secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code required');
      }

      // Get order
      const orderRef = admin.firestore().collection('orders').doc(orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      // Verify buyer
      if (orderData?.buyerId !== context.auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'Not authorized');
      }

      // Verify secret code
      if (orderData?.secretCode !== secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
      }

      // Update order status
      await orderRef.update({
        status: 'completed',
        fundsReleased: true,
        fundsReleasedAt: admin.firestore.Timestamp.now(),
        completedAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });

      console.log(`✅ Funds released for order: ${orderId}`);

      return {
        success: true,
        message: 'Funds released successfully'
      };

    } catch (error) {
      console.error('Error releasing funds:', error);
      throw error;
    }
  });

// Mark delivery completed (for sellers)
export const markDeliveryCompleted = functions
  .https.onCall(async (data, context) => {
    try {
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
      }

      const { orderId } = data;

      if (!orderId) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID required');
      }

      // Get order
      const orderRef = admin.firestore().collection('orders').doc(orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      // Verify seller
      if (orderData?.sellerId !== context.auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'Not authorized');
      }

      // Update order status to delivered
      await orderRef.update({
        status: 'delivered',
        deliveredAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });

      // Notify buyer using new notification system
      if (orderData?.buyerId) {
        try {
          // Create in-app notification
          await admin.firestore().collection(`users/${orderData.buyerId}/notifications`).add({
            type: 'order_delivered',
            title: 'Order Delivered!',
            message: `Your order has been delivered. Enter the secret code to release funds.`,
            icon: '/icons/icon-192.png',
            createdAt: admin.firestore.Timestamp.now(),
            read: false,
            link: `/orders/${orderId}`,
            priority: 'high',
            actionRequired: true,
            metadata: {},
            orderId: orderId
          });

          // Send push notification if available
          const tokenDoc = await admin.firestore()
            .doc(`users/${orderData.buyerId}/fcmTokens/web`)
            .get();

          if (tokenDoc.exists && tokenDoc.data()?.active && tokenDoc.data()?.token) {
            const payload: admin.messaging.Message = {
              token: tokenDoc.data()!.token,
              notification: {
                title: 'Order Delivered!',
                body: 'Your order has been delivered. Enter the secret code to release funds.',
                imageUrl: '/icons/icon-192.png'
              },
              data: {
                type: 'order_delivered',
                link: `/orders/${orderId}`,
                orderId: orderId,
                requireInteraction: 'true'
              },
              webpush: {
                headers: {
                  TTL: '86400',
                  Urgency: 'high'
                },
                notification: {
                  icon: '/icons/icon-192.png',
                  badge: '/icons/icon-96.png',
                  tag: 'order_delivered',
                  requireInteraction: true,
                  actions: [
                    { action: 'view', title: 'Release Funds' },
                    { action: 'dismiss', title: 'Dismiss' }
                  ],
                  vibrate: [200, 100, 200]
                }
              }
            };

            try {
              await admin.messaging().send(payload);
              console.log(`Push notification sent for order delivery to user ${orderData.buyerId}`);
            } catch (pushError) {
              console.error('Error sending delivery push notification:', pushError);
              if ((pushError as any).code === 'messaging/registration-token-not-registered') {
                await admin.firestore()
                  .doc(`users/${orderData.buyerId}/fcmTokens/web`)
                  .update({ active: false });
              }
            }
          }
        } catch (notificationError) {
          console.error('Error sending delivery notification:', notificationError);
        }
      }

      console.log(`✅ Order ${orderId} marked as delivered`);

      return {
        success: true,
        message: 'Order marked as delivered'
      };

    } catch (error) {
      console.error('Error marking delivery:', error);
      throw error;
    }
  });
